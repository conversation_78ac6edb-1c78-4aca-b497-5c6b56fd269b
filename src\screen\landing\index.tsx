"use client";

import { useState, useEffect } from "react";
import Cookies from "js-cookie";
import { useSession } from "next-auth/react";
import dynamic from "next/dynamic";

import PrimaryFilter from "@/components/globalComponents/primaryFilter";
import {
  PrimaryFilterSkeleton,
  RecommendationSkeleton,
  ChatSkeleton,
} from "@/components/loaders";
import { apiRequest } from "../../uitls/api";

// Dynamically load heavy components
const Recommendation = dynamic(() => import("./Recommendation"), {
  loading: () => <RecommendationSkeleton cardCount={3} />,
});
const ChatWithShasa = dynamic(
  () => import("@/components/globalComponents/ChatWithShasa"),
  {
    loading: () => <ChatSkeleton />,
  }
);

interface LandingPageProps {
  isLoading?: boolean;
  loadingComponents?: {
    filter?: boolean;
    chat?: boolean;
    recommendations?: boolean;
  };
}

const LandingPage = ({
  isLoading = false,
  loadingComponents = {},
}: LandingPageProps) => {
  const { data: session } = useSession();
  const [componentLoading, setComponentLoading] = useState({
    filter: loadingComponents.filter || isLoading,
    chat: loadingComponents.chat || isLoading,
    recommendations: loadingComponents.recommendations || isLoading,
  });

  // Guest login API
  const guestLogin = async () => {
    return await apiRequest({
      method: "POST",
      url: "/auth/guest-login",
      params: "",
      body: "",
      contentType: "",
      tokens: ``,
    });
  };

  // Run only once per session change
  useEffect(() => {
    const fetchGuest = async () => {
      const guestId = Cookies.get("guest_id");
      if (!session && !guestId) {
        try {
          const guestRes = await guestLogin();
          if (guestRes?.detail?.status === "success") {
            Cookies.set("guest_id", guestRes.detail.data.guest_id);
          }
        } catch (err) {
          console.error("Guest login error:", err);
        }
      }
    };
    fetchGuest();
  }, []);

  // Stop showing skeletons once data is ready
  useEffect(() => {
    if (!isLoading) {
      setComponentLoading({
        filter: false,
        chat: false,
        recommendations: false,
      });
    }
  }, [isLoading]);

  return (
    <div className="p-3 sm:p-5 rounded-tl-xl h-[calc(100vh-73px)] flex flex-col">
      <div>
        <p className="text-sm sm:text-base text-default-1000 opacity-75">
          Welcome to NxVoy!
        </p>
        <div className="text-2xl sm:text-4xl font-semibold text-subtitle">
          <span className="text-gradient">Meet Shasa</span>, Your Travel
          Companion!
        </div>
      </div>

      {/* Primary Filter Section */}
      <div className="py-3 sm:py-4">
        {componentLoading.filter ? <PrimaryFilterSkeleton /> : <PrimaryFilter />}
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-5 gap-3 sm:gap-4 flex-1 min-h-0">
        {/* Chat Section */}
        <div className="lg:col-span-3 order-2 lg:order-1">
          {componentLoading.chat ? <ChatSkeleton /> : <ChatWithShasa />}
        </div>

        {/* Recommendations Section */}
        <div className="lg:col-span-2 order-1 lg:order-2">
          {componentLoading.recommendations ? (
            <RecommendationSkeleton cardCount={3} />
          ) : (
            <Recommendation />
          )}
        </div>
      </div>
    </div>
  );
};

export default LandingPage;
