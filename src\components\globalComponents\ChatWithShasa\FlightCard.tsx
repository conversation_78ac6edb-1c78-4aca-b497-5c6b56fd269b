"use client";

import { useState } from "react";
import { Card, CardBody, Button } from "@heroui/react";
import Image from "next/image";
import { FaPlane } from "react-icons/fa";
import { IoIosArrowUp } from "react-icons/io";

export default function FlightCard({ flight, onSelectClick,onBookClick }: { flight: any; onSelectClick?: () => void; onBookClick?: () => void;
 }) {
  const f = flight.data;
  const [open, setOpen] = useState(true);

  return (
    <Card className="w-full rounded-2xl shadow-sm border border-gray-200 bg-[#EFEFFF]">
      <CardBody className="p-4">
        {/* Header */}
        <div
          className="flex items-center justify-between mb-3 cursor-pointer"
          onClick={() => setOpen(!open)}
        >
          <div className="flex items-center gap-2">
            <FaPlane className="text-gray-700 -rotate-90" />
            <span className="text-sm font-medium text-gray-700">Flight</span>
            <span className="font-semibold">
              {f.departure.airport} to {f.arrival.airport}
            </span>
          </div>
          <IoIosArrowUp
            className={`z-10 text-black ml-2 transform transition-transform duration-300 ${
              open ? "rotate-0" : "rotate-180"
            }`}
            size={20}
          />
        </div>

        {/* Collapsible Body */}
        {open && (
          <div className="flex items-start gap-3">
            {/* Airlines */}
            <div className="flex flex-col gap-3 justify-center border border-cardborder aspect-square w-[50px] h-[50px] rounded-lg p-2">
              <Image
                src={f.airline.logo}
                alt={f.airline.name}
                width={50}
                height={20}
                className="object-contain"
              />
              <Image
                src={f.airline.logo}
                alt={f.airline.name}
                width={50}
                height={20}
                className="object-contain"
              />
            </div>

            {/* Flight Details */}
            <div className="w-full">
              <div className="flex items-center justify-between">
                {/* Departure */}
                <div className="flex flex-col items-start">
                  <p className="text-lg font-semibold">{f.departure.time}</p>
                  <p className="text-xs text-gray-500">{f.departure.date}</p>
                  <p className="text-xs text-gray-500">{f.departure.city}</p>
                </div>

                {/* Path */}
                <div className="w-[80px] flex flex-col items-center text-default-700">
                  <div className="relative w-full h-6 flex items-center justify-center">
                    <div className="w-full border-t border-default-400 rotate-90 md:rotate-0" />
                    <FaPlane
                      className="z-10 text-default-400 rotate-90 md:rotate-0 absolute left-1/2 -translate-x-1/2"
                      size={24}
                    />
                  </div>
                  <p className="mt-1 text-xs font-medium text-subtitle">
                    {f.stops === 0 ? "Non-stop" : `${f.stops} stop`}
                  </p>
                  <p className="text-xs font-medium text-subtitle">{f.duration}</p>
                </div>

                {/* Arrival */}
                <div className="flex flex-col items-end">
                  <p className="text-lg font-semibold">{f.arrival.time}</p>
                  <p className="text-xs text-gray-500">{f.arrival.date}</p>
                  <p className="text-xs text-gray-500">{f.arrival.city}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Footer - Always Visible */}
        <div className= {open ? "flex items-center justify-between mt-4 border-t pt-3 ml-[55px]"
        :
         "flex items-center justify-between mt-4 border-t pt-3"
         }
        >
          <div>
            <p className="text-lg font-bold">
              {f.price.currency}
              {f.price.amount}
            </p>
            <p className="text-xs text-gray-500">Per person</p>
          </div>
          <Button
            color="primary"
            radius="full"
            size="sm"
            onPress={onSelectClick}
            variant="bordered"
          >
            Select Flight
          </Button>
        </div>
      </CardBody>
    </Card>
  );
}
